from .base import BaseClient
from .request_schema import (
    ProjectInfoRequest,
    ProjectPageBaseRequest,
    ProjectPageDynamicRequest,
    ProjectPageStaticRequest,
    ProjectTreeRequest,
)
from .response_schema import (
    ProjectInfoResponse,
    ProjectPageBaseResponse,
    ProjectPageDynamicResponse,
    ProjectPageStaticResponse,
    ProjectTreeResponse,
)


class ProjectClient(BaseClient):
    """项目实例信息相关接口客户端"""

    def get_info(self, req: ProjectInfoRequest) -> ProjectInfoResponse:
        """
        获取项目实例详细信息
        """
        params = req.model_dump(exclude_none=True)
        resp = self.request("GET", "/p/dt/v1/ins/project/info", params=params)
        return ProjectInfoResponse(**resp.json())

    def list(self) -> ProjectTreeResponse:
        """
        查询项目实例列表
        """
        resp = self.request("GET", "/p/dt/v1/ins/project/ins/list")
        return ProjectTreeResponse(**resp.json())

    def tree(self, req: ProjectTreeRequest) -> ProjectTreeResponse:
        """
        查询项目实例所有下级（树）
        """
        params = req.model_dump()
        resp = self.request("GET", "/p/dt/v1/ins/project/ins/tree", params=params)
        return ProjectTreeResponse(**resp.json())

    def page_base(self, req: ProjectPageBaseRequest) -> ProjectPageBaseResponse:
        """
        获取基础属性列表
        """
        resp = self.request(
            "POST", "/p/dt/v1/ins/project/page/base", json=req.model_dump()
        )
        return ProjectPageBaseResponse(**resp.json())

    def page_dynamic(
        self, req: ProjectPageDynamicRequest
    ) -> ProjectPageDynamicResponse:
        """
        获取动态属性列表
        """
        resp = self.request(
            "POST", "/p/dt/v1/ins/project/page/dynamic", json=req.model_dump()
        )
        return ProjectPageDynamicResponse(**resp.json())

    def page_static(self, req: ProjectPageStaticRequest) -> ProjectPageStaticResponse:
        """
        获取静态属性列表
        """
        resp = self.request(
            "POST", "/p/dt/v1/ins/project/page/static", json=req.model_dump()
        )
        return ProjectPageStaticResponse(**resp.json())
