import logging
from typing import Dict, Optional

import httpx

logger = logging.getLogger("meos_sdk")


class BaseClient:
    def __init__(self, base_url: str, token: Optional[str] = None, timeout: int = 10):
        self.base_url = base_url.rstrip("/")
        self.token = token
        self.timeout = timeout
        self._sync_client = httpx.Client(base_url=self.base_url, timeout=self.timeout)
        self._async_client = httpx.AsyncClient(
            base_url=self.base_url, timeout=self.timeout
        )

    def _get_headers(self) -> Dict[str, str]:
        headers = {}
        if self.token:
            headers["Siact-token"] = self.token
        return headers

    def request(self, method: str, url: str, **kwargs) -> httpx.Response:
        headers = kwargs.pop("headers", {})
        headers.update(self._get_headers())
        logger.info(f"[SYNC] {method.upper()} {url}")
        return self._sync_client.request(method, url, headers=headers, **kwargs)

    async def arequest(self, method: str, url: str, **kwargs) -> httpx.Response:
        headers = kwargs.pop("headers", {})
        headers.update(self._get_headers())
        logger.info(f"[ASYNC] {method.upper()} {url}")
        return await self._async_client.request(method, url, headers=headers, **kwargs)

    def set_token(self, token: str):
        self.token = token

    def close(self):
        self._sync_client.close()
        import asyncio

        if not self._async_client.is_closed:
            asyncio.create_task(self._async_client.aclose())
