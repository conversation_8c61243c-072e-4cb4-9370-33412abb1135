from typing import Optional

from pydantic import BaseModel, Field


class TokenRequest(BaseModel):
    appId: str = Field(..., description="应用ID")
    appSecret: str = Field(..., description="应用密钥")


class DyPropHistoryRequest(BaseModel):
    # 这里应根据 schema 递归生成所有字段，示例：
    # dataCode: str = Field(..., description="数据编码")
    # startTime: str = Field(..., description="开始时间")
    # endTime: str = Field(..., description="结束时间")
    ...


class DyPropRTRequest(BaseModel):
    # 这里应根据 schema 递归生成所有字段，示例：
    # dataCode: str = Field(..., description="数据编码")
    # ts: Optional[int] = Field(None, description="时间戳")
    ...


class DyPropRTFmCalcRequest(BaseModel):
    # 这里应根据 schema 递归生成所有字段，示例：
    # dataCode: str = Field(..., description="数据编码")
    # ts: Optional[int] = Field(None, description="时间戳")
    ...


class ProjectInfoRequest(BaseModel):
    dataCode: Optional[str] = Field(None, description="项目编码")


class ProjectTreeRequest(BaseModel):
    dataCode: str = Field(..., description="项目编码")


class ProjectPageBaseRequest(BaseModel):
    # 示例字段，实际应递归 schema 自动生成
    # page: int = Field(...)
    # size: int = Field(...)
    ...


class ProjectPageDynamicRequest(BaseModel): ...


class ProjectPageStaticRequest(BaseModel): ...
