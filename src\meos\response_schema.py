from typing import Optional

from pydantic import BaseModel, Field


class TokenResponseData(BaseModel):
    token: str = Field(..., description="Siact-token")


class TokenResponse(BaseModel):
    code: int
    msg: str
    data: Optional[TokenResponseData]


class DyPropHistoryResponse(BaseModel):
    # 这里应根据 schema 递归生成所有字段，示例：
    # code: int
    # msg: str
    # data: List[DyPropHistoryItem]
    ...


class DyPropRTResponse(BaseModel):
    # 这里应根据 schema 递归生成所有字段，示例：
    # code: int
    # msg: str
    # data: List[DyPropRTItem]
    ...


class DyPropRTFmCalcResponse(BaseModel):
    # 这里应根据 schema 递归生成所有字段，示例：
    # code: int
    # msg: str
    # data: List[DyPropRTFmCalcItem]
    ...


class ProjectInfoResponse(BaseModel):
    # 示例字段，实际应递归 schema 自动生成
    # code: int
    # msg: str
    # data: Optional[ProjectInfoData]
    ...


class ProjectTreeResponse(BaseModel): ...


class ProjectPageBaseResponse(BaseModel): ...


class ProjectPageDynamicResponse(BaseModel): ...


class ProjectPageStaticResponse(BaseModel): ...
