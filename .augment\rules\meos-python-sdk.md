---
type: "always_apply"
description: "MeOS操作系统接口-基于 MCP 提取的 OpenAPI 数据生成 Python SDK 并使用 Pydantic 建模"
---
## 🧠 提示词：基于 MCP 提取的 OpenAPI 数据生成 Python SDK 并使用 Pydantic 建模

### 🎯 目标说明：
请通过 MCP 协议调用我方已实现的功能模块，获取目标 OpenAPI 的解析结果。根据返回的接口定义数据，自动生成对应的 **Python SDK 客户端类封装代码**，并使用 **Pydantic 模型**对每个接口的请求参数和响应数据进行建模。

SDK 应支持**同步和异步**两种调用方式，并具有良好的可读性、可扩展性和类型安全。

---

### 🔧 功能要求：

#### 1. **调用 MCP 获取 OpenAPI 解析数据**
- 通过 MCP 调用指定工具, 获取接口详情。
- 获取解析后的结构化接口定义数据（如 paths、methods、parameters、requestBody、responses 等）。

#### 2. **生成 Python SDK 客户端类**
- 创建一个客户端类，封装所有 API 接口。
- 支持设置基础 URL 和默认请求头（如认证 token）。
- 使用 `httpx` 发起 HTTP 请求, 包括同步和异步请求, 所以每个接口应当具备两个接口：一个同步接口, 一个异步接口。
- 所有接口方法需具备类型注解（Type Hints）， 严格按照接口的入参和请求参数来，尤其是出参，要能够正确返回200的正确参数结构和错误类型的结构。
- 使用默认logging模块记录日志。

#### 3. **使用 Pydantic 模型封装入参与出参**
- 对每个接口的 `requestBody` 和 `parameters`，生成相应的 Pydantic 请求模型类。
- 对每个接口的 `responses`，生成相应的 Pydantic 响应模型类，200和其他错误类型如400、422等等也要按照接口中声明的去定义出来。
- 方法参数使用 Pydantic 模型进行校验与传递。
- 返回值也应为 Pydantic 模型对象或其列表。
- 示例:

```python
from typing import Annotated, Dict, Generic, List, Literal, Optional, TypeVar

from pydantic import Field, ValidationInfo, field_validator, model_validator


class _StartEndSchema(BaseModel):
    """起始值计算"""

    start_time: Annotated[Optional[str], Field(None, description="开始时间: 2024-07-01 00:00:00")]
    end_time: Annotated[Optional[str], Field(None, description="结束时间: 2024-07-01 23:00:00")]
    start_value: Annotated[Optional[Number], Field(None, description="起始值")]
    end_value: Annotated[Optional[Number], Field(None, description="结束值")]
    unit_increase: Annotated[Optional[Number], Field(None, description="单位增长值")]

    @model_validator(mode="after")
    def convert_str_to_datetime(self) -> Self:
        try:
            start_time = datetime.strptime(self.start_time, "%Y-%m-%d %H:%M:%S")
            end_time = datetime.strptime(self.end_time, "%Y-%m-%d %H:%M:%S")
        except Exception:
            raise ValueError("时间格式错误, 请使用 %Y-%m-%d %H:%M:%S 格式")
        else:
            if end_time < start_time:
                raise TimeCheckError("结束时间不能早于开始时间")

            return self

```

#### 4. **命名规范与组织建议**
- 类名格式：`{OpenAPI Title}Client`
- 模型类格式：`{OperationId}Request`, `{OperationId}Response`, 如果OperationId不存在, 则通过描述(summaries)进行翻译后使用合适的单词
- 创建类的标准: 如果接口有tag, 那么按照tag进行归类创建类进行管理. 应当避免将所有接口都放在同一个类中; 如果有些接口不归属任意tag, 那么应该创建通用类去管理
- 示例：`GetUserInfoRequest`, `GetUserInfoResponse`

---

### 📁 输入数据（由 MCP 提供）：
- 已解析的 OpenAPI 结构化数据（JSON 格式）
- 包含接口路径、方法、参数、请求体、响应体等信息
- （可选）用户指定的客户端名称、输出包名等

---

### 📤 输出结果：
- 一段完整的 Python SDK 封装代码，包含：
  - 客户端类（如 `SampleAPIClient`）
  - 各接口对应的方法函数
  - 所有接口使用的 Pydantic 模型类（输入/输出）

---

### 输出目录要求 
- 输入和输出定义应当放置为两个文件, 输入为 `request_schema.py`, 输出为 `response_schema.py`
- 通用操作放在一个 `base.py` 中。比如同步和异步请求的基本功能
- 针对不同tag创建的类, 应当有自己的py文件
- 最终所有api接口的定义应该在包的 `__init__.py` 中进行export

---

### ✅ 示例输入（来自 MCP 的解析结果片段）

```json
{
  "paths": {
    "/users/{userId}": {
      "get": {
        "operationId": "getUserInfo",
        "parameters": [
          {
            "name": "userId",
            "in": "path",
            "required": true,
            "schema": { "type": "integer" }
          }
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "id": { "type": "integer" },
                    "name": { "type": "string" },
                    "email": { "type": "string" }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
```

---

### ✅ 示例输出（Python SDK + Pydantic 模型）

```python
from pydantic import BaseModel
import httpx

# Pydantic Models
class GetUserInfoRequest(BaseModel):
    userId: Annotated[int, Field(description="用户ID")]

class GetUserInfoResponse(BaseModel):
    id: Annotated[int, Field(description="用户ID")]
    name: Annotated[str, Field(description="用户名称")]
    email: Annotated[str, Field(description="用户邮箱")]

# Client Class
class SampleAPIClient:
    def __init__(self, base_url="https://api.example.com/v1", headers=None):
        self.base_url = base_url
        self.headers = headers or {}
        self.client = httpx.Client(base_url=base_url, headers=headers)

    def get_user_info(self, request: GetUserInfoRequest) -> GetUserInfoResponse:
        response = self.client.get(f"/users/{request.userId}")
        return GetUserInfoResponse(**response.json())
```

---

### 🧩 扩展建议（可作为后续 Prompt 的延伸）
- 支持异步客户端（使用 `httpx.AsyncClient`）
- 自动生成单元测试
- 支持接口分组、分页、重试机制等高级功能
- 集成日志记录、异常处理
