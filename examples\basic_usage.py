"""
Basic usage examples for MeOS Python SDK.

This example demonstrates the most common use cases for the MeOS SDK.
"""

import asyncio
from datetime import datetime, timedelta
from meos import MeOSClient, AsyncMeOSClient


def basic_sync_example():
    """Basic synchronous client usage example."""
    # Initialize the client
    client = MeOSClient(
        base_url="https://your-meos-instance.com",
        app_id="your_app_id",
        app_secret="your_app_secret"
    )
    
    try:
        # Get project information
        project_info = client.instances.get_project_info("PROJECT_CODE")
        print(f"Project Name: {project_info.pro_name}")
        print(f"Project Type: {project_info.pro_type}")
        
        # Query dynamic data for the last hour
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        dynamic_data = client.data.get_dynamic_history(
            data_codes=["DATA_CODE_1", "DATA_CODE_2"],
            start_time=start_time.strftime("%Y-%m-%d %H:%M:%S"),
            end_time=end_time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
        print(f"Retrieved {len(dynamic_data)} data points")
        for data_point in dynamic_data[:5]:  # Show first 5 points
            print(f"  {data_point.data_code}: {data_point.value} at {data_point.time}")
            
        # Get real-time data
        realtime_data = client.data.get_dynamic_realtime(
            data_codes=["DATA_CODE_1", "DATA_CODE_2"]
        )
        
        print(f"Real-time data:")
        for data_point in realtime_data:
            print(f"  {data_point.data_code}: {data_point.value}")
            
    except Exception as e:
        print(f"Error: {e}")


async def basic_async_example():
    """Basic asynchronous client usage example."""
    # Initialize the async client
    async with AsyncMeOSClient(
        base_url="https://your-meos-instance.com",
        app_id="your_app_id",
        app_secret="your_app_secret"
    ) as client:
        
        try:
            # Get project information
            project_info = await client.instances.get_project_info("PROJECT_CODE")
            print(f"Project Name: {project_info.pro_name}")
            
            # Query multiple data sources concurrently
            tasks = [
                client.data.get_dynamic_realtime(["DATA_CODE_1"]),
                client.data.get_dynamic_realtime(["DATA_CODE_2"]),
                client.instances.get_station_info("STATION_CODE")
            ]
            
            results = await asyncio.gather(*tasks)
            realtime_data_1, realtime_data_2, station_info = results
            
            print(f"Data 1: {realtime_data_1[0].value if realtime_data_1 else 'No data'}")
            print(f"Data 2: {realtime_data_2[0].value if realtime_data_2 else 'No data'}")
            print(f"Station: {station_info.name if station_info else 'No station'}")
            
        except Exception as e:
            print(f"Error: {e}")


def pagination_example():
    """Example of handling paginated results."""
    client = MeOSClient(
        base_url="https://your-meos-instance.com",
        app_id="your_app_id",
        app_secret="your_app_secret"
    )
    
    try:
        # Get paginated project list
        page_size = 10
        current_page = 1
        
        while True:
            projects = client.instances.list_projects(
                page=current_page,
                size=page_size
            )
            
            print(f"Page {current_page}: {len(projects.records)} projects")
            for project in projects.records:
                print(f"  - {project.pro_name} ({project.data_code})")
            
            # Check if there are more pages
            if current_page >= projects.pages:
                break
                
            current_page += 1
            
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    print("=== Basic Sync Example ===")
    basic_sync_example()
    
    print("\n=== Basic Async Example ===")
    asyncio.run(basic_async_example())
    
    print("\n=== Pagination Example ===")
    pagination_example()
