from .base import BaseClient
from .request_schema import DyPropHistoryRequest, DyPropRTFmCalcRequest, DyPropRTRequest
from .response_schema import (
    DyPropHistoryResponse,
    DyPropRTFmCalcResponse,
    DyPropRTResponse,
)


class DyPropClient(BaseClient):
    """动态属性数据相关接口客户端"""

    def get_history(self, req: DyPropHistoryRequest) -> DyPropHistoryResponse:
        """
        获取动态属性历史数据
        """
        resp = self.request("POST", "/p/dt/v1/public/ins/dy/history", json=req.dict())
        return DyPropHistoryResponse(**resp.json())

    async def aget_history(self, req: DyPropHistoryRequest) -> DyPropHistoryResponse:
        """
        获取动态属性历史数据（异步）
        """
        resp = await self.arequest(
            "POST", "/p/dt/v1/public/ins/dy/history", json=req.dict()
        )
        return DyPropHistoryResponse(**resp.json())

    def get_rt(self, req: DyPropRTRequest) -> DyPropRTResponse:
        """
        获取动态属性实时数据
        """
        resp = self.request("POST", "/p/dt/v1/public/ins/dy/rt", json=req.dict())
        return DyPropRTResponse(**resp.json())

    async def aget_rt(self, req: DyPropRTRequest) -> DyPropRTResponse:
        """
        获取动态属性实时数据（异步）
        """
        resp = await self.arequest("POST", "/p/dt/v1/public/ins/dy/rt", json=req.dict())
        return DyPropRTResponse(**resp.json())

    def get_rt_fm_calc(self, req: DyPropRTFmCalcRequest) -> DyPropRTFmCalcResponse:
        """
        获取动态属性实时数据（含公式计算）
        """
        resp = self.request(
            "POST", "/p/dt/v1/public/ins/dy/rt/fm/calc", json=req.dict()
        )
        return DyPropRTFmCalcResponse(**resp.json())

    async def aget_rt_fm_calc(
        self, req: DyPropRTFmCalcRequest
    ) -> DyPropRTFmCalcResponse:
        """
        获取动态属性实时数据（含公式计算，异步）
        """
        resp = await self.arequest(
            "POST", "/p/dt/v1/public/ins/dy/rt/fm/calc", json=req.dict()
        )
        return DyPropRTFmCalcResponse(**resp.json())
