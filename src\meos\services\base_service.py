"""
Base service class for MeOS Python SDK.

This module provides the foundation for all service classes.
"""

import logging
from typing import Any, Dict, List, Optional, Type, TypeVar, Union

import httpx
from pydantic import BaseModel

from ..base import BaseHTTPClient
from ..exceptions import MeOSAPIError, MeOSValidationError
from ..models.base import BaseResponse

logger = logging.getLogger(__name__)

T = TypeVar("T", bound=BaseModel)
R = TypeVar("R", bound=BaseResponse)


class BaseService:
    """Base service class providing common functionality."""
    
    def __init__(self, client: BaseHTTPClient) -> None:
        """
        Initialize the service.
        
        Args:
            client: HTTP client instance
        """
        self.client = client
    
    def _validate_request_data(self, data: BaseModel) -> Dict[str, Any]:
        """
        Validate and serialize request data.
        
        Args:
            data: Request data model
            
        Returns:
            Serialized data dictionary
            
        Raises:
            MeOSValidationError: If validation fails
        """
        try:
            return data.model_dump(by_alias=True, exclude_none=True)
        except Exception as e:
            raise MeOSValidationError(f"Request validation failed: {e}")
    
    def _parse_response(
        self,
        response: httpx.Response,
        response_model: Type[R],
    ) -> R:
        """
        Parse HTTP response into a response model.
        
        Args:
            response: HTTP response
            response_model: Response model class
            
        Returns:
            Parsed response model
            
        Raises:
            MeOSAPIError: If response parsing fails
        """
        try:
            response_data = response.json()
        except Exception as e:
            raise MeOSAPIError(
                f"Failed to parse response JSON: {e}",
                status_code=response.status_code,
            )
        
        try:
            return response_model.model_validate(response_data)
        except Exception as e:
            raise MeOSAPIError(
                f"Failed to parse response data: {e}",
                status_code=response.status_code,
                response_data=response_data,
            )
    
    def _make_request(
        self,
        method: str,
        url: str,
        response_model: Type[R],
        json_data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> R:
        """
        Make a synchronous HTTP request.
        
        Args:
            method: HTTP method
            url: Request URL
            response_model: Response model class
            json_data: JSON request body
            params: Query parameters
            **kwargs: Additional request parameters
            
        Returns:
            Parsed response model
        """
        # Prepare request parameters
        request_kwargs = {}
        if json_data is not None:
            request_kwargs["json"] = json_data
        if params is not None:
            request_kwargs["params"] = params
        request_kwargs.update(kwargs)
        
        # Make request
        response = self.client.request(method, url, **request_kwargs)
        
        # Parse response
        return self._parse_response(response, response_model)
    
    async def _make_async_request(
        self,
        method: str,
        url: str,
        response_model: Type[R],
        json_data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> R:
        """
        Make an asynchronous HTTP request.
        
        Args:
            method: HTTP method
            url: Request URL
            response_model: Response model class
            json_data: JSON request body
            params: Query parameters
            **kwargs: Additional request parameters
            
        Returns:
            Parsed response model
        """
        # Prepare request parameters
        request_kwargs = {}
        if json_data is not None:
            request_kwargs["json"] = json_data
        if params is not None:
            request_kwargs["params"] = params
        request_kwargs.update(kwargs)
        
        # Make request
        response = await self.client.arequest(method, url, **request_kwargs)
        
        # Parse response
        return self._parse_response(response, response_model)
    
    def _build_query_params(self, **kwargs: Any) -> Dict[str, Any]:
        """
        Build query parameters from keyword arguments.
        
        Args:
            **kwargs: Keyword arguments
            
        Returns:
            Query parameters dictionary
        """
        params = {}
        for key, value in kwargs.items():
            if value is not None:
                if isinstance(value, list):
                    # Handle list parameters
                    params[key] = ",".join(str(v) for v in value)
                else:
                    params[key] = str(value)
        return params
    
    def _extract_data_from_response(self, response: BaseResponse[T]) -> T:
        """
        Extract data from response and handle errors.
        
        Args:
            response: Response model
            
        Returns:
            Response data
            
        Raises:
            MeOSAPIError: If response indicates an error
        """
        if not response.is_success:
            raise MeOSAPIError(
                response.msg,
                code=response.code,
            )
        
        if response.data is None:
            raise MeOSAPIError(
                "Response data is None",
                code=response.code,
            )
        
        return response.data
    
    def _extract_data_list_from_response(
        self, 
        response: BaseResponse[List[T]]
    ) -> List[T]:
        """
        Extract data list from response and handle errors.
        
        Args:
            response: Response model with list data
            
        Returns:
            Response data list
            
        Raises:
            MeOSAPIError: If response indicates an error
        """
        if not response.is_success:
            raise MeOSAPIError(
                response.msg,
                code=response.code,
            )
        
        if response.data is None:
            return []
        
        return response.data
    
    def _log_request(self, method: str, url: str, **kwargs: Any) -> None:
        """Log request details."""
        logger.debug(f"Making {method.upper()} request to {url}")
        if kwargs.get("json"):
            logger.debug(f"Request body: {kwargs['json']}")
        if kwargs.get("params"):
            logger.debug(f"Query params: {kwargs['params']}")
    
    def _log_response(self, response: BaseResponse) -> None:
        """Log response details."""
        logger.debug(f"Response: code={response.code}, msg={response.msg}")
        if hasattr(response, "data") and response.data:
            logger.debug(f"Response data type: {type(response.data)}")
    
    @property
    def base_url(self) -> str:
        """Get the base URL from the client."""
        return self.client.config.base_url
